import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

import { AppComponent } from './app.component';
import { ComstarRmbRobotTestComponent } from './comstar-rmb-robot-test/comstar-rmb-robot-test.component';
import { ComstarRmbRobotModule } from '../../projects/comstar-rmb-robot/src/lib/comstar-rmb-robot.module';

@NgModule({
  declarations: [
    AppComponent,
    ComstarRmbRobotTestComponent
  ],
  imports: [
    BrowserModule,
    CommonModule,
    FormsModule,
    ComstarRmbRobotModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
