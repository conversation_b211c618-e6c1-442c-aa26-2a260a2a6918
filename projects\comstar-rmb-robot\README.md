# 开发地址：/Projects 目录下 已有项目可直接将 src 拷入 lib 目录下，并做响应调整

# 全局搜索 mybank 替换为本机构简称 （注意大小写场景）

# isAlive 须实现 resolve(false)

# onDestroy()方法中须使用 super.onDestroy();删除监听(接收)通道 this.serviceBus.deleteMessage("通道 ID");

# messageOn 和 messageSend 方法为同步 监听通道放在发送通道前面 可避免第一次发送获取信息请求，接收不到数据的情况

# 写好代码后 运行 npm run build 编译无误即可 插件本身没有界面，直接 npm run start 编译报错不影响开发，可忽略

# 日志打印方式： this.logService.info({moduleId:'日志关键字',message:'消息内容'+JSON.stringify(data)}) 接收消息及发送消息均打印日志，方便排查异常情况

# 插件开发完成后，使用绿色包测试时，确保测试场景覆盖所有业务内容，测试无误发送邮件，审核提交，提高效率
