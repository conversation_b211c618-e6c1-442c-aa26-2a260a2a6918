<div class="test-container">
  <h1>ComstarRmbRobot 组件测试页面</h1>
  
  <!-- 组件实例 -->
  <div class="component-section">
    <h2>组件实例</h2>
    <div class="component-wrapper">
      <comstar-rmb-robot #robotComponent></comstar-rmb-robot>
    </div>
  </div>

  <!-- 测试控制面板 -->
  <div class="test-controls">
    <h2>测试控制面板</h2>
    
    <!-- 测试参数配置 -->
    <div class="test-params">
      <h3>测试参数配置</h3>
      
      <!-- 消息测试参数 -->
      <div class="param-group">
        <h4>消息测试参数</h4>
        <div class="form-row">
          <label>消息内容:</label>
          <input type="text" [(ngModel)]="testMessage.body" placeholder="输入测试消息内容">
        </div>
        <div class="form-row">
          <label>内容类型:</label>
          <input type="text" [(ngModel)]="testMessage.content_type" placeholder="text">
        </div>
        <div class="form-row">
          <label>接收者:</label>
          <input type="text" [(ngModel)]="testMessage.receiver" placeholder="接收者ID">
        </div>
        <div class="form-row">
          <label>发送者:</label>
          <input type="text" [(ngModel)]="testMessage.sender" placeholder="发送者ID">
        </div>
      </div>

      <!-- 日志测试参数 -->
      <div class="param-group">
        <h4>日志测试参数</h4>
        <div class="form-row">
          <label>日志级别:</label>
          <select [(ngModel)]="testLevel">
            <option value="info">info</option>
            <option value="error">error</option>
            <option value="debug">debug</option>
          </select>
        </div>
        <div class="form-row">
          <label>日志内容:</label>
          <input type="text" [(ngModel)]="testContent" placeholder="输入测试日志内容">
        </div>
        <div class="form-row">
          <label>TraceId:</label>
          <input type="text" [(ngModel)]="testTraceId" placeholder="自动生成或手动输入">
        </div>
      </div>
    </div>

    <!-- 测试按钮 -->
    <div class="test-buttons">
      <h3>方法测试</h3>
      <div class="button-group">
        <button (click)="testIsAlive()" class="test-btn">测试 isAlive()</button>
        <button (click)="testGenerateTraceId()" class="test-btn">测试 generateTraceId()</button>
        <button (click)="testPrintLogV2()" class="test-btn">测试 printLogV2()</button>
        <button (click)="testSendMsgToIDeal()" class="test-btn">测试 sendMsgToIDeal()</button>
        <button (click)="testCloseWebsocket()" class="test-btn">测试 closeWebsocket()</button>
      </div>
      <div class="button-group">
        <button (click)="testAllMethods()" class="test-btn-primary">测试所有方法</button>
        <button (click)="clearResults()" class="test-btn-secondary">清空结果</button>
      </div>
    </div>
  </div>

  <!-- isAlive 结果显示 -->
  <div class="alive-result" *ngIf="isAliveResult !== null">
    <h3>isAlive() 方法结果</h3>
    <div class="result-item" [class.success]="isAliveResult" [class.error]="!isAliveResult">
      <strong>组件存活状态:</strong> {{ isAliveResult ? '存活' : '未存活' }}
    </div>
  </div>

  <!-- 测试结果显示 -->
  <div class="test-results">
    <h2>测试结果 ({{ testResults.length }} 条记录)</h2>
    
    <div class="results-container" *ngIf="testResults.length > 0">
      <div class="result-item" 
           *ngFor="let result of testResults; trackBy: trackByResultId"
           [class.success]="result.success" 
           [class.error]="!result.success">
        <div class="result-header">
          <span class="method-name">{{ result.methodName }}</span>
          <span class="timestamp">{{ result.timestamp }}</span>
          <span class="status" [class.success]="result.success" [class.error]="!result.success">
            {{ result.success ? '成功' : '失败' }}
          </span>
        </div>
        <div class="result-content">
          <div class="input-section" *ngIf="result.input && result.input !== '{}'">
            <strong>输入参数:</strong>
            <pre>{{ result.input }}</pre>
          </div>
          <div class="output-section">
            <strong>输出结果:</strong>
            <pre>{{ result.output }}</pre>
          </div>
        </div>
      </div>
    </div>
    
    <div class="no-results" *ngIf="testResults.length === 0">
      <p>暂无测试结果，请点击上方按钮开始测试</p>
    </div>
  </div>

  <!-- 使用说明 -->
  <div class="instructions">
    <h2>使用说明</h2>
    <ul>
      <li><strong>isAlive():</strong> 检查组件是否存活，返回Promise&lt;boolean&gt;</li>
      <li><strong>generateTraceId():</strong> 生成日志追踪ID</li>
      <li><strong>printLogV2():</strong> 打印格式化日志，支持info和error级别</li>
      <li><strong>sendMsgToIDeal():</strong> 发送消息给iDeal客户端</li>
      <li><strong>closeWebsocket():</strong> 关闭WebSocket连接</li>
      <li><strong>监听方法:</strong> monitorCurrentUser(), monitorDialogueMsg(), monitorFriends() 在组件初始化时自动调用</li>
    </ul>
    
    <div class="note">
      <h4>注意事项:</h4>
      <ul>
        <li>某些方法依赖于@ctaf框架的ServiceBus，在测试环境中可能无法完全模拟真实行为</li>
        <li>日志输出请查看浏览器控制台</li>
        <li>WebSocket相关功能需要在真实的iDeal环境中才能完全测试</li>
        <li>消息发送功能会调用ServiceBus的messageSend方法</li>
      </ul>
    </div>
  </div>
</div>
