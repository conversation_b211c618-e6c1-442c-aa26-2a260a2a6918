import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { ComponentsModule } from '@ctaf/components';
import { EcologyMeta, EcologyModule, EcologyService } from '@ctaf/ecology';
import { FrameworkModule, PageService } from '@ctaf/framework';
import { ComstarRmbRobotComponent } from './comstar-rmb-robot.component';

@NgModule({
  declarations: [ComstarRmbRobotComponent],
  imports: [
    EcologyModule,
    BrowserModule,
    CommonModule,
    FormsModule,
    FrameworkModule,
    ComponentsModule,
  ],
  providers: [],
  bootstrap: [ComstarRmbRobotComponent],
})
export class ComstarRmbRobotModule {}

EcologyService.registerEcologyWidget(
  new EcologyMeta('comstar-rmb-robot', ComstarRmbRobotComponent)
);
PageService.setConfig('plugin-info', {
  name: 'aif-security-robot',
  pluginType: 0,
});
