import { Component, ViewChild, OnInit } from '@angular/core';
import { ComstarRmbRobotComponent } from '../../../projects/comstar-rmb-robot/src/lib/comstar-rmb-robot.component';

@Component({
  selector: 'app-comstar-rmb-robot-test',
  templateUrl: './comstar-rmb-robot-test.component.html',
  styleUrls: ['./comstar-rmb-robot-test.component.less']
})
export class ComstarRmbRobotTestComponent implements OnInit {
  @ViewChild(ComstarRmbRobotComponent) robotComponent!: ComstarRmbRobotComponent;
  
  // 测试数据
  testMessage = {
    body: '测试消息内容',
    content_type: 'text',
    receiver: 'test_receiver',
    sender: 'test_sender'
  };
  
  testLevel = 'info';
  testContent = '这是一条测试日志';
  testTraceId = '';
  
  // 测试结果显示
  testResults: any[] = [];
  isAliveResult: boolean | null = null;
  
  constructor() { }

  ngOnInit(): void {
    this.testTraceId = this.generateTestTraceId();
  }

  /**
   * 生成测试用的TraceId
   */
  generateTestTraceId(): string {
    return 'test-trace-' + new Date().getTime();
  }

  /**
   * 添加测试结果
   */
  addTestResult(methodName: string, input: any, output: any, success: boolean): void {
    this.testResults.push({
      timestamp: new Date().toLocaleString(),
      methodName,
      input: JSON.stringify(input),
      output: JSON.stringify(output),
      success,
      id: Date.now()
    });
  }

  /**
   * 清空测试结果
   */
  clearResults(): void {
    this.testResults = [];
    this.isAliveResult = null;
  }

  /**
   * 测试 isAlive 方法
   */
  async testIsAlive(): Promise<void> {
    try {
      if (this.robotComponent) {
        const result = await this.robotComponent.isAlive();
        this.isAliveResult = result;
        this.addTestResult('isAlive', {}, { result }, true);
      } else {
        this.addTestResult('isAlive', {}, { error: '组件未初始化' }, false);
      }
    } catch (error) {
      this.addTestResult('isAlive', {}, { error: error }, false);
    }
  }

  /**
   * 测试 generateTraceId 方法
   */
  testGenerateTraceId(): void {
    try {
      if (this.robotComponent) {
        const result = this.robotComponent.generateTraceId();
        this.addTestResult('generateTraceId', {}, { traceId: result }, true);
      } else {
        this.addTestResult('generateTraceId', {}, { error: '组件未初始化' }, false);
      }
    } catch (error) {
      this.addTestResult('generateTraceId', {}, { error: error }, false);
    }
  }

  /**
   * 测试 printLogV2 方法
   */
  testPrintLogV2(): void {
    try {
      if (this.robotComponent) {
        this.robotComponent.printLogV2(this.testLevel, this.testContent, this.testTraceId);
        this.addTestResult('printLogV2', 
          { level: this.testLevel, content: this.testContent, traceId: this.testTraceId }, 
          { message: '日志已打印，请查看控制台' }, 
          true
        );
      } else {
        this.addTestResult('printLogV2', 
          { level: this.testLevel, content: this.testContent, traceId: this.testTraceId }, 
          { error: '组件未初始化' }, 
          false
        );
      }
    } catch (error) {
      this.addTestResult('printLogV2', 
        { level: this.testLevel, content: this.testContent, traceId: this.testTraceId }, 
        { error: error }, 
        false
      );
    }
  }

  /**
   * 测试 sendMsgToIDeal 方法
   */
  testSendMsgToIDeal(): void {
    try {
      if (this.robotComponent) {
        this.robotComponent.sendMsgToIDeal(this.testMessage, this.testTraceId);
        this.addTestResult('sendMsgToIDeal', 
          { message: this.testMessage, traceId: this.testTraceId }, 
          { message: '消息已发送，请查看控制台日志' }, 
          true
        );
      } else {
        this.addTestResult('sendMsgToIDeal', 
          { message: this.testMessage, traceId: this.testTraceId }, 
          { error: '组件未初始化' }, 
          false
        );
      }
    } catch (error) {
      this.addTestResult('sendMsgToIDeal', 
        { message: this.testMessage, traceId: this.testTraceId }, 
        { error: error }, 
        false
      );
    }
  }

  /**
   * 测试 closeWebsocket 方法
   */
  testCloseWebsocket(): void {
    try {
      if (this.robotComponent) {
        this.robotComponent.closeWebsocket();
        this.addTestResult('closeWebsocket', {}, { message: 'WebSocket关闭方法已调用' }, true);
      } else {
        this.addTestResult('closeWebsocket', {}, { error: '组件未初始化' }, false);
      }
    } catch (error) {
      this.addTestResult('closeWebsocket', {}, { error: error }, false);
    }
  }

  /**
   * 测试所有公共方法
   */
  testAllMethods(): void {
    this.clearResults();
    
    // 按顺序测试所有方法
    setTimeout(() => this.testGenerateTraceId(), 100);
    setTimeout(() => this.testPrintLogV2(), 200);
    setTimeout(() => this.testSendMsgToIDeal(), 300);
    setTimeout(() => this.testIsAlive(), 400);
    setTimeout(() => this.testCloseWebsocket(), 500);
  }

  /**
   * 更新测试消息
   */
  updateTestMessage(): void {
    // 这个方法用于从模板中更新测试消息
  }

  /**
   * 更新测试日志参数
   */
  updateTestLogParams(): void {
    // 这个方法用于从模板中更新测试日志参数
  }

  /**
   * TrackBy函数用于优化ngFor性能
   */
  trackByResultId(index: number, item: any): any {
    return item.id;
  }
}
