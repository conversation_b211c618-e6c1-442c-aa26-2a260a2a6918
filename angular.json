{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ecology-robot-template": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/ecology-robot-template", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./node_modules/@ctaf/components/assets/components/html", "output": "assets/components/html"}], "styles": ["src/styles.less", "node_modules/@ctaf/components/assets/components/3rd/malihucustomscrollbarplugin/jquery.mCustomScrollbar.css"], "scripts": ["./node_modules/jquery/dist/jquery.min.js", "./node_modules/@ctaf/framework/assets/cometd.js", "./node_modules/@ctaf/framework/assets/jquery.cometd.js", "./node_modules/moment/moment.js", "./node_modules/@ctaf/components/assets/components/3rd/plugin.js", "./node_modules/jquery-mousewheel/jquery.mousewheel.js", "./node_modules/@ctaf/components/assets/components/3rd/malihucustomscrollbarplugin/jquery.mCustomScrollbar.js", "./node_modules/@ctaf/components/assets/components/3rd/iscroll/iscroll.js"], "baseHref": ""}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "none"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "ecology-robot-template:build:production"}, "development": {"browserTarget": "ecology-robot-template:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ecology-robot-template:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less"], "scripts": []}}}}, "comstar-rmb-robot": {"projectType": "library", "root": "projects/comstar-rmb-robot", "sourceRoot": "projects/comstar-rmb-robot/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/comstar-rmb-robot/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/comstar-rmb-robot/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/comstar-rmb-robot/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/comstar-rmb-robot/src/test.ts", "tsConfig": "projects/comstar-rmb-robot/tsconfig.spec.json", "karmaConfig": "projects/comstar-rmb-robot/karma.conf.js"}}}}}, "defaultProject": "ecology-robot-template"}