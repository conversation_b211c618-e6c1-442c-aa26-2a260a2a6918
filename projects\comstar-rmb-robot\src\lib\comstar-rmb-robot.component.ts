import { ChangeDetectorRef, Component, Optional } from '@angular/core';
import { ServiceBus, EcologyWidget } from '@ctaf/ecology';
import { Log } from '@ctaf/framework';
declare let $: any;

@Component({
  selector: 'comstar-rmb-robot',
  templateUrl: './comstar-rmb-robot.component.html',
  styleUrls: ['./comstar-rmb-robot.component.less'],
  providers: [ServiceBus],
})
export class ComstarRmbRobotComponent extends EcologyWidget {
  // 对手方客户端->本方客户端，插件监听此聊天消息发送给后端
  private TO_MYBANK_MESSAGE_ID: string = '101001';

  // 插件->对手方客户端，插件发送此聊天消息给iDeal
  private TO_OPPOSITE_MESSAGE_ID: string = '201001';

  // 本方客户端->对手方客户端，一般是交易员从本方客户端人工发送的聊天消息，插件监听此聊天消息做对应处理
  private MANUAL_TO_OPPOSIT_MESSAGE_ID: string = '101002';

  // 获取当前登录用户信息
  private SELF_INFO_ID: string = '101101';

  // 监听好友信息
  private SELF_FRIEND_INFO_ID: string = '101201';

  // 发起获取好友的请求
  private SEND_SELF_FRIEND_REQUEST_ID: string = '201201';

  private client: any = null;
  constructor(cdr: ChangeDetectorRef, @Optional() serviceBus: ServiceBus) {
    super(cdr, serviceBus);
    this.monitorCurrentUser();
    this.monitorDialogueMsg();
    this.monitorFriends();
  }
  onInit() {
    super.onInit();
  }

  onDestroy() {
    this.serviceBus.deleteMessage(this.SELF_FRIEND_INFO_ID);
    this.serviceBus.deleteMessage(this.TO_MYBANK_MESSAGE_ID);
    this.serviceBus.deleteMessage(this.SELF_INFO_ID);
    this.serviceBus.deleteMessage(this.MANUAL_TO_OPPOSIT_MESSAGE_ID);
    super.onDestroy();
  }

  //每10s调用一次，用于判断插件是否自动关闭，如果是false，那么就关闭插件
  public isAlive() {
    return new Promise<boolean>((resolve, reject) => {
      resolve(true);
    });
  }
  /**
   * 监听iDeal发送的登录用户信息
   */
  monitorCurrentUser(): void {
    this.serviceBus.messageOn(this.SELF_INFO_ID, (message) => {
      const traceId = this.generateTraceId();
      this.printLogV2(
        'info',
        '接收到当前登录用户的信息：' + JSON.stringify(message),
        traceId
      );
    });
  }
  /**
   * 监听从iDeal发送出来的聊天消息
   * 包括对手方发送的，本方人工发送的
   */
  monitorDialogueMsg(): void {
    // 监听对手方发送过来的消息
    this.serviceBus.messageOn(this.TO_MYBANK_MESSAGE_ID, (message) => {
      const { body, content_type, receiver, sender } = message;
      const traceId = this.generateTraceId();
      this.sendMsgToIDeal(
        {
          body,
          content_type,
          receiver: sender,
          sender: receiver,
        },
        traceId
      );
    });

    // 监听我方iDeal发送出的消息，如果是特定消息则执行特定操作
    this.serviceBus.messageOn(this.MANUAL_TO_OPPOSIT_MESSAGE_ID, (message) => {
      const { body } = message;
      if (`${body}`.includes('好友')) {
        const traceId = this.generateTraceId();
        this.printLogV2('info', '向iDeal发送获取好友信息的请求', traceId);
        this.serviceBus.messageSend(this.SEND_SELF_FRIEND_REQUEST_ID, '');
      }
    });
  }

  //监听好友信息
  monitorFriends(): void {
    this.serviceBus.messageOn(this.SELF_FRIEND_INFO_ID, (message) => {
      const traceId = this.generateTraceId();
      //注意，好友信息日志较多，实际生产环境需要尽量减少好友日志打印，有可能会导致ideal客户端卡住
      this.printLogV2(
        'info',
        '获取到当前用户的好友信息' + JSON.stringify(message),
        traceId
      );
    });
  }

  /**
   * 发送消息给对手方iDeal客户端
   *
   * @param message 要发送的消息
   * @param traceId 日志trace
   */
  sendMsgToIDeal(message: any, traceId: string): void {
    this.printLogV2(
      'info',
      '发送消息给iDeal客户端：' + JSON.stringify(message),
      traceId
    );
    const { body = '' } = message;
    //发送空消息到ideal里面，有可能触发后续聊天消息不展示bug
    if (body === '' || body === null) {
      this.printLogV2('info', '发送消息为空，不向iDeal发送', traceId);
      return;
    }
    this.serviceBus.messageSend(this.TO_OPPOSITE_MESSAGE_ID, message);
  }

  /**
   * 生成日志traceId
   *
   * @returns traceId
   */
  generateTraceId(): string {
    return 'traceId-' + new Date().getTime();
  }

  /**
   * 格式化日志
   *
   * @param level     日志级别
   * @param content   日志内容
   * @param traceId   traceId
   */
  printLogV2(level: string, content: string, traceId: string): void {
    if (!level) {
      return;
    }
    const finalLog: string =
      '[交易机器人widget]' + content + ' - ' + '[' + traceId + ']';

    if (level === 'info') {
      this.logService.info(new Log(new Date().toLocaleString(), finalLog));
      return;
    }
    if (level === 'error') {
      this.logService.error(new Log(new Date().toLocaleString(), finalLog));
      return;
    }
  }

  /**
   * 取消websocket订阅
   */
  closeWebsocket() {
    if (this.client) {
      this.client.deactivate();
    }
  }
}
