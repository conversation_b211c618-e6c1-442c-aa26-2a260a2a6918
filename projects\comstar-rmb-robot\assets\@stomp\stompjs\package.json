{"name": "@stomp/stompjs", "version": "7.0.0", "description": "STOMP client for Javascript and Typescript", "scripts": {"clean": "rm -rf bundles esm6", "rollup": "rollup -c && rm -rf bundles/*.d.ts bundles/compatibility/", "build": "npm run clean && npx tsc && npm run rollup && cp spec/package.json bundles", "test": "jasmine", "karma": "karma start spec/karma.conf.js --single-run", "lint": "tslint 'src/**/*.ts'", "prettier": "prettier --write .", "test-helpers": "tsc -p spec/helpers-src/tsconfig.json", "prepublishOnly": "npm run lint && npm run build && npm run karma && npm run test"}, "repository": {"type": "git", "url": "https://github.com/stomp-js/stompjs.git"}, "keywords": ["STOMP", "RabbitMQ", "ActiveMQ", "Websocket", "messaging", "queue", "SockJS"], "author": "<EMAIL>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/stomp-js/stompjs/issues"}, "homepage": "https://github.com/stomp-js/stompjs#readme", "devDependencies": {"@chiragrupani/karma-chromium-edge-launcher": "^2.2.2", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "jasmine": "^4.3.0", "karma": "^6.4.0", "karma-chrome-launcher": "^3.1.1", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^5.1.0", "karma-safari-launcher": "https://github.com/RLovelett/karma-safari-launcher.git#safari-webdriver", "karma-summary-reporter": "^3.1.1", "onchange": "^7.1.0", "prettier": "^2.7.1", "rollup": "^3.9.1", "text-encoding": "^0.7.0", "ts-loader": "^9.3.1", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.8.2", "ws": "^8.8.1"}, "type": "module", "exports": {"import": "./esm6/index.js", "require": "./bundles/stomp.umd.js"}, "browser": "./bundles/stomp.umd.js", "typings": "index.d.ts", "sideEffects": false}