{"version": 3, "file": "compat-client.js", "sourceRoot": "", "sources": ["../../src/compatibility/compat-client.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAGtC,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAEpD;;;;;;;;GAQG;AACH,MAAM,OAAO,YAAa,SAAQ,MAAM;IAMtC;;;;;;;OAOG;IACH,YAAY,gBAA2B;QACrC,KAAK,EAAE,CAAC;QAdV;;WAEG;QACI,0BAAqB,GAAW,EAAE,GAAG,IAAI,CAAC;QAoOzC,mBAAc,GAAkB,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;QAxN9D,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,gCAAgC;QAChC,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,OAAc,EAAE,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;QAC1B,CAAC,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,GAAG,IAAW;QAClC,IAAI,kBAAkB,CAAC;QACvB,IAAI,eAAe,CAAC;QACpB,IAAI,aAAa,CAAC;QAClB,IAAI,OAAO,GAAiB,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QACD,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;YACjC,CAAC,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,kBAAkB,CAAC,GAAG,IAAI,CAAC;SACtE;aAAM;YACL,QAAQ,IAAI,CAAC,MAAM,EAAE;gBACnB,KAAK,CAAC;oBACJ;wBACE,OAAO,CAAC,KAAK;wBACb,OAAO,CAAC,QAAQ;wBAChB,eAAe;wBACf,aAAa;wBACb,kBAAkB;wBAClB,OAAO,CAAC,IAAI;qBACb,GAAG,IAAI,CAAC;oBACT,MAAM;gBACR;oBACE;wBACE,OAAO,CAAC,KAAK;wBACb,OAAO,CAAC,QAAQ;wBAChB,eAAe;wBACf,aAAa;wBACb,kBAAkB;qBACnB,GAAG,IAAI,CAAC;aACZ;SACF;QAED,OAAO,CAAC,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACI,OAAO,CAAC,GAAG,IAAW;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC;QAExC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACV,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAC9B;QACD,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACV,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SACzB;QACD,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACV,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAC5B;QACD,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACV,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAChC;QAED,KAAK,CAAC,QAAQ,EAAE,CAAC;IACnB,CAAC;IAED;;;;;;;;;;OAUG;IACI,UAAU,CACf,kBAAwB,EACxB,UAAwB,EAAE;QAE1B,IAAI,kBAAkB,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC;SACxC;QACD,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;QAEjC,KAAK,CAAC,UAAU,EAAE,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACI,IAAI,CACT,WAAmB,EACnB,UAAkC,EAAE,EACpC,OAAe,EAAE;QAEjB,OAAO,GAAI,MAAc,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAE9C,MAAM,uBAAuB,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,KAAK,CAAC;QACpE,IAAI,uBAAuB,EAAE;YAC3B,OAAO,OAAO,CAAC,gBAAgB,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,OAAO,CAAC;YACX,WAAW;YACX,OAAO,EAAE,OAAuB;YAChC,IAAI;YACJ,uBAAuB;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,IAAI,eAAe,CAAC,KAAa;QAC/B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,IAAI,SAAS,CAAC,KAA0B;QACtC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,IAAI,SAAS,CAAC,KAAwB;QACpC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClC,CAAC;IAID;;;;;OAKG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACH,IAAI,SAAS,CAAC,KAA6C;QACzD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC1C,CAAC;CACF"}