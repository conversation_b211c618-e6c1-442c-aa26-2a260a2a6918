{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAGlD,OAAO,EACL,eAAe,EAOf,gBAAgB,GAEjB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAUzC;;;;GAIG;AACH,MAAM,OAAO,MAAM;IAyTjB;;OAEG;IACH,YAAY,OAAoB,EAAE;QA/SlC;;;;;;;;WAQG;QACI,kBAAa,GAAG,QAAQ,CAAC,OAAO,CAAC;QAyBxC;;;WAGG;QACI,sBAAiB,GAAW,CAAC,CAAC;QAKrC;;WAEG;QACI,mBAAc,GAAW,IAAI,CAAC;QAErC;;WAEG;QACI,sBAAiB,GAAW,KAAK,CAAC;QAEzC;;WAEG;QACI,sBAAiB,GAAW,KAAK,CAAC;QAEzC;;;;;;;;;;;;WAYG;QACI,qBAAgB,GAAY,KAAK,CAAC;QAEzC;;;WAGG;QACI,0BAAqB,GAAW,CAAC,GAAG,IAAI,CAAC;QAEhD;;;;;;;WAOG;QACI,wBAAmB,GAAY,KAAK,CAAC;QAE5C;;;;;;;;;WASG;QACI,gCAA2B,GAAY,KAAK,CAAC;QAyJpD;;;;;;;WAOG;QACI,kCAA6B,GAAY,KAAK,CAAC;QA8BtD;;;;;WAKG;QACI,UAAK,GAAoB,eAAe,CAAC,QAAQ,CAAC;QAQvD,kBAAkB;QAClB,MAAM,IAAI,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,8EAA8E;QAC9E,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAE7B,sBAAsB;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAhOD;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC;IACxC,CAAC;IASD;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED,IAAI,iBAAiB,CAAC,KAAmB;QACvC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;SAChE;IACH,CAAC;IA+BD;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;IAC9D,CAAC;IAmGD;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC;IAC9E,CAAC;IAID;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,MAAM,CAAC;IAC/C,CAAC;IASO,YAAY,CAAC,KAAsB;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAuCD;;OAEG;IACI,SAAS,CAAC,IAAiB;QAChC,qCAAqC;QACpC,MAAc,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;OAMG;IACI,QAAQ;QACb,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;gBAC3D,OAAO;aACR;YAED,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE1C,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC;QAEF,oEAAoE;QACpE,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,YAAY,EAAE;YAC/C,IAAI,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1B,SAAS,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,SAAS,EAAE,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;YAC5E,OAAO;SACR;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,KAAK,CACR,8DAA8D,CAC/D,CAAC;YACF,OAAO;SACR;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,EAAE;YAC9B,cAAc;YACd,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACvC;YACD,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,EAAE;gBACxC,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,OAAO;iBACR;gBACD,0DAA0D;gBAC1D,mCAAmC;gBACnC,IAAI,CAAC,KAAK,CACR,iCAAiC,IAAI,CAAC,iBAAiB,oBAAoB,CAC5E,CAAC;gBACF,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAEpC,iDAAiD;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE1C,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE;YACrD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,2BAA2B,EAAE,IAAI,CAAC,2BAA2B;YAC7D,6BAA6B,EAAE,IAAI,CAAC,6BAA6B;YAEjE,SAAS,EAAE,KAAK,CAAC,EAAE;gBACjB,sDAAsD;gBACtD,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAC3B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACtC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;iBACrC;gBAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAChB,IAAI,CAAC,KAAK,CACR,sEAAsE,CACvE,CAAC;oBACF,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC5B,OAAO;iBACR;gBACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;YACD,YAAY,EAAE,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YACD,YAAY,EAAE,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YACD,gBAAgB,EAAE,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,mDAAmD;gBAEnF,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,YAAY,EAAE;oBAC/C,6BAA6B;oBAC7B,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;iBAC7C;gBAED,qFAAqF;gBACrF,uCAAuC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAE3B,IAAI,IAAI,CAAC,MAAM,EAAE;oBACf,IAAI,CAAC,mBAAmB,EAAE,CAAC;iBAC5B;YACH,CAAC;YACD,gBAAgB,EAAE,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,kBAAkB,EAAE,OAAO,CAAC,EAAE;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;YACD,kBAAkB,EAAE,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;YACD,gBAAgB,EAAE,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAEO,gBAAgB;QACtB,IAAI,SAAuB,CAAC;QAE5B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACrC;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,SAAS,GAAG,IAAI,SAAS,CACvB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CACtC,CAAC;SACH;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;SAC1E;QACD,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC;QACrC,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,qCAAqC,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;YAEzE,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;SACzB;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,KAAK,CAAC,UAAU,CAAC,UAA+B,EAAE;QACvD,MAAM,KAAK,GAAY,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QAClC,IAAI,UAAyB,CAAC;QAE9B,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,QAAQ,EAAE;YAC3C,IAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACnD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QAED,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEhD,wCAAwC;QACxC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;SAC/B;QAED,IACE,IAAI,CAAC,aAAa;YAClB,mEAAmE;YACnE,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,gBAAgB,CAAC,MAAM,EACrD;YACA,MAAM,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACjE,wDAAwD;YACxD,UAAU,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACjD,wCAAwC;gBACxC,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,GAAG,CAAC,EAAE;oBAC1C,oBAAoB,CAAC,GAAG,CAAC,CAAC;oBAC1B,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,qDAAqD;YACrD,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC5C,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QAED,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,aAAa,EAAE,gBAAgB,EAAE,CAAC;SACxC;aAAM,IAAI,aAAa,EAAE;YACxB,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC7B;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC;SACtC;IACH,CAAC;IAEO,oBAAoB;QAC1B,wBAAwB;QACxB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;SAC9B;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACI,OAAO,CAAC,MAAsB;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAC;SAChE;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACI,eAAe,CAAC,SAAiB,EAAE,QAA2B;QACnE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,SAAS,CACd,WAAmB,EACnB,QAA6B,EAC7B,UAAwB,EAAE;QAE1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;;;;;;OAWG;IACI,WAAW,CAAC,EAAU,EAAE,UAAwB,EAAE;QACvD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,aAAsB;QACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,aAAqB;QACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,aAAqB;QAChC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,GAAG,CACR,SAAiB,EACjB,cAAsB,EACtB,UAAwB,EAAE;QAE1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,IAAI,CACT,SAAiB,EACjB,cAAsB,EACtB,UAAwB,EAAE;QAE1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;CACF"}