# IDeal 机器人插件-银河

## 说明

    node_modules需要找中心要

这个是插件核心代码地址 `projects\comstar-rmb-robot\src\lib\comstar-rmb-robot.component.ts`

## 开发流程

1. 编写好逻辑代码
2. `npm run build`编译代码
3. 把编译好的插件 `dist\comstar-rmb-robot\dist\comstar-rmb-robot.min.js` 或 `dist\comstar-rmb-robot\umd\comstar-rmb-robot.umd.js`放到 `iDeal\extensions\robot` 目录下，进行测试 (extensions\robot 文件夹是没有的，需要自己创建)
4. 插件测试无误，
5. 把代码以邮件方式发送给中心，然后由中心老师进行编译成 DLL 文件，再放到客户的生产 ideal 里面 (extensions 目录,robot 目录不需要)`注意：发送源码之前注意检查本地的依赖`

## 调试

1. 打开 ideal 客户端 `iDeal\resources\app\setting.ini`文件
2. 设置 `devXTools=true` 如没有就添加
3. 在 ideal 客户端，通过快捷键 `ctrl + shift + k`打开控制台

## Demo 测试

1. 对手方发送消息给插件方，插件方会回复一条一样的消息回去
2. 插件方在聊天窗口输入好友，日志内会打印好友信息
