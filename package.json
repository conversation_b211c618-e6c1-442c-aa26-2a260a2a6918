{"name": "comstarRmbRobot", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.config.json", "build": "gulp build", "watch": "ng build --watch --configuration development", "test": "ng test", "publish": "gulp publish", "unpublish": "gulp unpublish", "pack": "gulp pack"}, "private": true, "dependencies": {"@angular/animations": "~13.2.0", "@angular/common": "~13.2.0", "@angular/compiler": "~13.2.0", "@angular/core": "~13.2.0", "@angular/forms": "~13.2.0", "@angular/platform-browser": "~13.2.0", "@angular/platform-browser-dynamic": "~13.2.0", "@angular/router": "~13.2.0", "@ctaf/components": "^3.1.40", "@ctaf/config": "^3.1.40", "@ctaf/ecology": "^3.1.40", "@ctaf/framework": "^3.1.40", "gulp": "^4.0.2", "moment": "^2.30.1", "rxjs": "~7.5.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.2.6", "@angular/cli": "~13.2.6", "@angular/compiler-cli": "~13.2.0", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "ng-packagr": "^13.0.0", "typescript": "~4.5.2"}}