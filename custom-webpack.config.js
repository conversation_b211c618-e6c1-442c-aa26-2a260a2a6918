
const webpack = require('webpack');
const pkg = require('./package.json');
module.exports = (config, options) => {
  config.externals = {
    "@angular/core": "require('@angular/core')",
    "@angular/forms": "require('@angular/forms')",
    "@angular/common": "require('@angular/common')",
    "@angular/platform-browser": "require('@angular/platform-browser')",
    "@angular/animations": "require('@angular/animations')",
    "@angular/platform": "require('@angular/platform')",
    "@ctaf/framework": "require('@ctaf/framework')",
    "@ctaf/components": "require('@ctaf/components')",
    "@ctaf/ecology": "require('@ctaf/ecology')",
    "keycode": "require('keycode')",
    "keycode/index": "require('keycode/index')",
  };
  config.plugins.push(new webpack.DefinePlugin({
    APP_VERSION: JSON.stringify(pkg.version),
  }));
  config.resolve.fallback = {
    "crypto": false
  }
  return config;
};

    