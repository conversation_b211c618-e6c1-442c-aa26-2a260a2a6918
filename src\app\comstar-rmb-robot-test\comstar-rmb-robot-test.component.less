.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
  }

  h2 {
    color: #34495e;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #3498db;
    padding-left: 10px;
  }

  h3 {
    color: #2c3e50;
    margin-bottom: 10px;
  }

  h4 {
    color: #7f8c8d;
    margin-bottom: 8px;
  }
}

.component-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;

  .component-wrapper {
    background: white;
    border: 2px dashed #3498db;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.test-controls {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-params {
  margin-bottom: 20px;

  .param-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;

    .form-row {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      label {
        min-width: 100px;
        font-weight: 600;
        color: #495057;
        margin-right: 10px;
      }

      input, select {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: #3498db;
          box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
      }
    }
  }
}

.test-buttons {
  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
  }

  .test-btn, .test-btn-primary, .test-btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
  }

  .test-btn {
    background: #3498db;
    color: white;

    &:hover {
      background: #2980b9;
    }
  }

  .test-btn-primary {
    background: #27ae60;
    color: white;
    font-size: 16px;
    padding: 12px 24px;

    &:hover {
      background: #229954;
    }
  }

  .test-btn-secondary {
    background: #95a5a6;
    color: white;

    &:hover {
      background: #7f8c8d;
    }
  }
}

.alive-result {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-results {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .results-container {
    max-height: 600px;
    overflow-y: auto;
  }

  .result-item {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 15px;
    padding: 15px;
    transition: all 0.3s ease;

    &.success {
      border-left: 4px solid #27ae60;
      background: #d5f4e6;
    }

    &.error {
      border-left: 4px solid #e74c3c;
      background: #fdeaea;
    }

    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding-bottom: 8px;
      border-bottom: 1px solid #dee2e6;

      .method-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 16px;
      }

      .timestamp {
        color: #7f8c8d;
        font-size: 12px;
      }

      .status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;

        &.success {
          background: #27ae60;
          color: white;
        }

        &.error {
          background: #e74c3c;
          color: white;
        }
      }
    }

    .result-content {
      .input-section, .output-section {
        margin-bottom: 10px;

        strong {
          color: #2c3e50;
          display: block;
          margin-bottom: 5px;
        }

        pre {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 10px;
          margin: 0;
          font-size: 12px;
          white-space: pre-wrap;
          word-wrap: break-word;
          max-height: 200px;
          overflow-y: auto;
        }
      }
    }
  }

  .no-results {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 40px;
  }
}

.instructions {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  ul {
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      line-height: 1.5;

      strong {
        color: #2c3e50;
      }
    }
  }

  .note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-top: 20px;

    h4 {
      color: #856404;
      margin-bottom: 10px;
    }

    ul li {
      color: #856404;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-container {
    padding: 10px;
  }

  .test-buttons .button-group {
    flex-direction: column;
  }

  .result-item .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .param-group .form-row {
    flex-direction: column;
    align-items: flex-start;

    label {
      margin-bottom: 5px;
    }

    input, select {
      width: 100%;
    }
  }
}
