global.baseurl = __dirname;
global.widgettype = "widget";
const gulp = require("gulp");
global.pkgname = "@ctaf";
// global.widgetsasync = [{
//     name: "",
//     selector: "",
//     component: "",
//     title: "",
//     files:[],
//     icon: "",
//     iconimage: "",
//     locals: {}
// }]
const {
  cleanAll,
  genVersionJson,
  flattenGreen,
  unpublishAll,
  buildAllLib,
  publishAllLib,
} = require("@ctaf/config/gulpfile");
const { copyFile, removeFile } = require("@ctaf/config/utils");
global.concatConfig = {
  "comstar-rmb-robot": {
    before: [
      {
        name: "crypto-js",
        src: ["./projects/comstar-rmb-robot/assets/crypto-js/crypto-js.js"],
        exports: `
                ;if(modules){modules['crypto-js'] = module.exports};
            `,
      },
      {
        name: "@stomp/stompjs",
        src: [
          "./projects/comstar-rmb-robot/assets/@stomp/stompjs/bundles/stomp.umd.min.js",
        ],
        exports: `
                ;if(modules){modules['@stomp/stompjs'] = exports};
            `,
      },
      /*  {
        name: "crypto-js",
        src: ["./node_modules/crypto-js/crypto-js.js"],
        exports: `
                ;if(modules){modules['crypto-js'] = module.exports};
            `,
      },
      {
        name: "@stomp/stompjs",
        src: ["./node_modules/@stomp/stompjs/bundles/stomp.umd.min.js"],
        exports: `
                ;if(modules){modules['@stomp/stompjs'] = exports};
            `,
      }, */
    ],
  },
};
async function buildAll() {
  await buildAllLib(__dirname);
}

async function publishAll() {
  await publishAllLib();
}
// 卸载
async function unpublishpkg(cb) {
  await unpublishAll(cb);
}
async function flatten(cb) {
  await flattenGreen(cb);
}
async function getVersion() {
  await genVersionJson(global.pkgname);
}
async function movePkg() {
  //deploy
  if (resource && dest) {
    removeFile(dest);
    copyFile(resource, dest);
  }
}
exports.copyFile = movePkg;
exports.build = gulp.series(cleanAll, buildAll);
exports.publish = gulp.series(publishAll);
exports.unpublish = unpublishpkg;
exports.pack = gulp.series(flatten, getVersion);
